<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['activeBrewing' => null, 'completedPotions' => [], 'userProfile']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['activeBrewing' => null, 'completedPotions' => [], 'userProfile']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<div class="mb-4">
    <h3 class="text-[#e5b769] font-semibold text-base mb-2 border-b border-[#3d3a2e] pb-1">Рабочий стол алхимика</h3>

    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
        
        <div id="active-brewing"
            class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-2"
            data-active="<?php echo e(isset($activeBrewing) || (isset($completedPotions) && count($completedPotions) > 0) ? 'true' : 'false'); ?>"
            data-completed="<?php echo e((isset($completedPotions) && count($completedPotions) > 0) ? 'true' : 'false'); ?>">
            <div class="flex items-center justify-between mb-2">
                <span class="text-[#d3c6a6] text-sm font-medium">Котёл</span>
                <?php if(isset($activeBrewing)): ?>
                    <span class="text-[#7cfc00] text-xs px-2 py-0.5 bg-[#1a301a] rounded border border-[#397239]">
                        Варится
                    </span>
                <?php elseif(isset($completedPotions) && count($completedPotions) > 0): ?>
                    <span class="text-[#ffd700] text-xs px-2 py-0.5 bg-[#3d3a2e] rounded border border-[#a6925e]">
                        Готово
                    </span>
                <?php else: ?>
                    <span class="text-[#8a8a8a] text-xs px-2 py-0.5 bg-[#2a2722] rounded border border-[#514b3c]">
                        Пусто
                    </span>
                <?php endif; ?>
            </div>

            
            <div class="bg-[#1a1814] rounded border border-[#3d3a2e] p-2 min-h-[80px] flex items-center justify-center">
                <?php if(isset($activeBrewing)): ?>
                    
                    <div class="text-center">
                        <div class="text-2xl mb-1">🧪</div>
                        <div class="text-[#d3c6a6] text-xs"><?php echo e($activeBrewing->recipe->name ?? 'Неизвестное зелье'); ?></div>
                        <div class="text-[#7cfc00] text-xs mt-1" id="brewing-timer">
                            Осталось: <span id="time-remaining">Загрузка...</span>
                        </div>
                    </div>
                <?php elseif(isset($completedPotions) && count($completedPotions) > 0): ?>
                    
                    <div class="text-center">
                        <div class="text-2xl mb-1">✨</div>
                        <div class="text-[#ffd700] text-xs">Готово зелий: <?php echo e(count($completedPotions)); ?></div>
                        <button id="collect-all-potions"
                            class="mt-2 bg-gradient-to-b from-[#4a452c] to-[#2a2721] text-[#e5b769] text-xs px-3 py-1 rounded border border-[#8c784e] hover:from-[#5a552c] hover:to-[#3a3721] transition-all duration-300">
                            Собрать все
                        </button>
                    </div>
                <?php else: ?>
                    
                    <div class="text-center text-[#8a8a8a]">
                        <div class="text-2xl mb-1">🍯</div>
                        <div class="text-xs">Котёл пуст</div>
                        <div class="text-xs mt-1">Выберите рецепт для варки</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        
        <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-2">
            <div class="flex items-center justify-between mb-2">
                <span class="text-[#d3c6a6] text-sm font-medium">Информация</span>
            </div>
            
            <div class="bg-[#1a1814] rounded border border-[#3d3a2e] p-2 min-h-[80px]">
                <?php if(isset($activeBrewing)): ?>
                    
                    <div class="text-xs text-[#d3c6a6] space-y-1">
                        <div><span class="text-[#e5b769]">Рецепт:</span> <?php echo e($activeBrewing->recipe->name ?? 'Неизвестно'); ?></div>
                        <div><span class="text-[#e5b769]">Время варки:</span> <?php echo e($activeBrewing->recipe->brewing_time ?? 0); ?> мин</div>
                        <div><span class="text-[#e5b769]">Опыт:</span> +<?php echo e($activeBrewing->recipe->experience_reward ?? 0); ?></div>
                    </div>
                <?php elseif(isset($completedPotions) && count($completedPotions) > 0): ?>
                    
                    <div class="text-xs text-[#d3c6a6] space-y-1">
                        <?php $__currentLoopData = $completedPotions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $potion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex justify-between">
                                <span><?php echo e($potion->name ?? 'Зелье'); ?></span>
                                <span class="text-[#ffd700]">x<?php echo e($potion->quantity ?? 1); ?></span>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    
                    <div class="text-xs text-[#8a8a8a] space-y-1">
                        <div>• Выберите рецепт из списка</div>
                        <div>• Убедитесь в наличии ингредиентов</div>
                        <div>• Нажмите "Начать варку"</div>
                        <div class="mt-2 text-[#d3c6a6]">
                            <span class="text-[#e5b769]">Уровень алхимии:</span> <?php echo e($userProfile->alchemy_level ?? 1); ?>

                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/alchemist/workspace.blade.php ENDPATH**/ ?>