<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['userRecipes' => [], 'selectedRecipeId' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['userRecipes' => [], 'selectedRecipeId' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<div class="mb-4">
    <h3 class="text-[#e5b769] font-semibold text-base mb-2 border-b border-[#3d3a2e] pb-1">Доступные рецепты</h3>
    
    <?php if($userRecipes && count($userRecipes) > 0): ?>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
            <?php $__currentLoopData = $userRecipes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userRecipe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $recipe = $userRecipe->recipe;
                    $isSelected = $selectedRecipeId && $selectedRecipeId == $recipe->id;
                ?>
                
                <div class="recipe-card cursor-pointer transition-all duration-300 
                    <?php echo e($isSelected ? 'ring-2 ring-[#a6925e] bg-gradient-to-b from-[#3d3a2e] to-[#2a2721]' : 'bg-gradient-to-b from-[#2a2722] to-[#1d1916] hover:from-[#3d3a2e] hover:to-[#2a2721]'); ?> 
                    border border-[#514b3c] rounded-md p-2"
                    data-recipe-id="<?php echo e($recipe->id); ?>"
                    data-recipe-name="<?php echo e($recipe->name); ?>"
                    data-brewing-time="<?php echo e($recipe->brewing_time); ?>"
                    data-experience="<?php echo e($recipe->experience_reward); ?>"
                    data-ingredients="<?php echo e(json_encode($recipe->ingredients->map(function($ingredient) {
                        return [
                            'id' => $ingredient->id,
                            'name' => $ingredient->name,
                            'quantity' => $ingredient->pivot->quantity ?? 1,
                            'icon' => $ingredient->icon_path ?? 'assets/ingredients/default.png'
                        ];
                    }))); ?>">
                    
                    
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-sm font-medium text-[#e5b769] truncate"><?php echo e($recipe->name); ?></h4>
                        <span class="text-xs text-[#d3c6a6] bg-[#1a1814] px-1 py-0.5 rounded">
                            x<?php echo e($userRecipe->quantity); ?>

                        </span>
                    </div>
                    
                    
                    <?php if($recipe->description): ?>
                        <p class="text-xs text-[#d3c6a6] mb-2 line-clamp-2"><?php echo e($recipe->description); ?></p>
                    <?php endif; ?>
                    
                    
                    <div class="text-xs text-[#998d66] space-y-1 mb-2">
                        <div class="flex justify-between">
                            <span>Время варки:</span>
                            <span class="text-[#d3c6a6]"><?php echo e($recipe->brewing_time); ?> мин</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Опыт:</span>
                            <span class="text-[#c1a96e]">+<?php echo e($recipe->experience_reward); ?></span>
                        </div>
                        <?php if($recipe->required_level > 1): ?>
                            <div class="flex justify-between">
                                <span>Требуемый уровень:</span>
                                <span class="text-[#e5b769]"><?php echo e($recipe->required_level); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    
                    <div class="border-t border-[#3d3a2e] pt-2">
                        <div class="text-xs text-[#998d66] mb-1">Ингредиенты:</div>
                        <div class="flex flex-wrap gap-1">
                            <?php $__currentLoopData = $recipe->ingredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center bg-[#1a1814] rounded px-1 py-0.5 border border-[#3d3a2e]">
                                    <?php if($ingredient->icon_path): ?>
                                        <img src="<?php echo e(asset($ingredient->icon_path)); ?>" 
                                             alt="<?php echo e($ingredient->name); ?>" 
                                             class="w-3 h-3 mr-1"
                                             onerror="this.src='<?php echo e(asset('assets/ingredients/default.png')); ?>'">
                                    <?php endif; ?>
                                    <span class="text-xs text-[#d3c6a6]">
                                        <?php echo e($ingredient->pivot->quantity ?? 1); ?>x <?php echo e($ingredient->name); ?>

                                    </span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    
                    
                    <?php if($isSelected): ?>
                        <div class="mt-2 text-center">
                            <span class="text-xs text-[#c1a96e] bg-[#1a1814] px-2 py-1 rounded border border-[#a6925e]">
                                ✓ Выбран
                            </span>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php else: ?>
        
        <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-4 text-center">
            <div class="text-[#8a8a8a] text-sm">
                <div class="text-2xl mb-2">📜</div>
                <div>У вас нет рецептов для алхимии</div>
                <div class="text-xs mt-1">Найдите или купите рецепты у торговцев</div>
            </div>
        </div>
    <?php endif; ?>
</div>


<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/alchemist/recipes-list.blade.php ENDPATH**/ ?>