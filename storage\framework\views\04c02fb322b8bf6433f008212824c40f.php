<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['userIngredients' => [], 'userCatalysts' => []]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['userIngredients' => [], 'userCatalysts' => []]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<div class="mb-4">
    <h3 class="text-[#e5b769] font-semibold text-base mb-2 border-b border-[#3d3a2e] pb-1">Ингредиенты и катализаторы</h3>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        
        <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-3">
            <h4 class="text-sm font-medium text-[#d3c6a6] mb-2 flex items-center">
                <span class="mr-2">🌿</span>
                Ингредиенты
            </h4>
            
            <div class="bg-[#1a1814] rounded border border-[#3d3a2e] p-2 max-h-40 overflow-y-auto">
                <?php if($userIngredients && count($userIngredients) > 0): ?>
                    <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                        <?php $__currentLoopData = $userIngredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userIngredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php $ingredient = $userIngredient->ingredient; ?>
                            <div class="bg-[#252117] border border-[#3d3a2e] rounded p-2 text-center hover:bg-[#2a2722] transition-colors duration-200">
                                
                                <?php if($ingredient->icon_path): ?>
                                    <img src="<?php echo e(asset($ingredient->icon_path)); ?>" 
                                         alt="<?php echo e($ingredient->name); ?>" 
                                         class="w-8 h-8 mx-auto mb-1"
                                         onerror="this.src='<?php echo e(asset('assets/ingredients/default.png')); ?>'">
                                <?php else: ?>
                                    <div class="w-8 h-8 mx-auto mb-1 bg-[#3d3a2e] rounded flex items-center justify-center">
                                        <span class="text-xs">🌿</span>
                                    </div>
                                <?php endif; ?>
                                
                                
                                <div class="text-xs text-[#d3c6a6] truncate" title="<?php echo e($ingredient->name); ?>">
                                    <?php echo e($ingredient->name); ?>

                                </div>
                                <div class="text-xs text-[#c1a96e] font-medium">
                                    x<?php echo e($userIngredient->quantity); ?>

                                </div>
                                
                                
                                <?php if($ingredient->rarity): ?>
                                    <div class="text-xs mt-1">
                                        <?php switch($ingredient->rarity):
                                            case ('common'): ?>
                                                <span class="text-[#8a8a8a]">Обычный</span>
                                                <?php break; ?>
                                            <?php case ('uncommon'): ?>
                                                <span class="text-[#7cfc00]">Необычный</span>
                                                <?php break; ?>
                                            <?php case ('rare'): ?>
                                                <span class="text-[#1e90ff]">Редкий</span>
                                                <?php break; ?>
                                            <?php case ('epic'): ?>
                                                <span class="text-[#9932cc]">Эпический</span>
                                                <?php break; ?>
                                            <?php case ('legendary'): ?>
                                                <span class="text-[#ffd700]">Легендарный</span>
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center text-[#8a8a8a] py-4">
                        <div class="text-lg mb-1">🌿</div>
                        <div class="text-xs">Нет ингредиентов</div>
                        <div class="text-xs mt-1">Найдите ингредиенты в мире</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        
        <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-3">
            <h4 class="text-sm font-medium text-[#d3c6a6] mb-2 flex items-center">
                <span class="mr-2">💎</span>
                Катализаторы
            </h4>
            
            <div class="bg-[#1a1814] rounded border border-[#3d3a2e] p-2 max-h-40 overflow-y-auto">
                <?php if($userCatalysts && count($userCatalysts) > 0): ?>
                    <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                        <?php $__currentLoopData = $userCatalysts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userCatalyst): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php $catalyst = $userCatalyst->catalyst; ?>
                            <div class="bg-[#252117] border border-[#3d3a2e] rounded p-2 text-center hover:bg-[#2a2722] transition-colors duration-200">
                                
                                <?php if($catalyst->icon_path): ?>
                                    <img src="<?php echo e(asset($catalyst->icon_path)); ?>" 
                                         alt="<?php echo e($catalyst->name); ?>" 
                                         class="w-8 h-8 mx-auto mb-1"
                                         onerror="this.src='<?php echo e(asset('assets/catalysts/default.png')); ?>'">
                                <?php else: ?>
                                    <div class="w-8 h-8 mx-auto mb-1 bg-[#3d3a2e] rounded flex items-center justify-center">
                                        <span class="text-xs">💎</span>
                                    </div>
                                <?php endif; ?>
                                
                                
                                <div class="text-xs text-[#d3c6a6] truncate" title="<?php echo e($catalyst->name); ?>">
                                    <?php echo e($catalyst->name); ?>

                                </div>
                                <div class="text-xs text-[#c1a96e] font-medium">
                                    x<?php echo e($userCatalyst->quantity); ?>

                                </div>
                                
                                
                                <?php if($catalyst->effect_description): ?>
                                    <div class="text-xs text-[#998d66] mt-1 truncate" title="<?php echo e($catalyst->effect_description); ?>">
                                        <?php echo e($catalyst->effect_description); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center text-[#8a8a8a] py-4">
                        <div class="text-lg mb-1">💎</div>
                        <div class="text-xs">Нет катализаторов</div>
                        <div class="text-xs mt-1">Катализаторы улучшают зелья</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    
    <div class="mt-3 bg-[#1a1814] border border-[#3d3a2e] rounded p-2">
        <div class="text-xs text-[#998d66] space-y-1">
            <div class="flex items-center">
                <span class="text-[#c1a96e] mr-2">💡</span>
                <span>Ингредиенты необходимы для создания зелий по рецептам</span>
            </div>
            <div class="flex items-center">
                <span class="text-[#c1a96e] mr-2">✨</span>
                <span>Катализаторы могут улучшить качество и эффекты зелий</span>
            </div>
            <div class="flex items-center">
                <span class="text-[#c1a96e] mr-2">🔍</span>
                <span>Найти ингредиенты можно в различных локациях мира</span>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/alchemist/ingredients-panel.blade.php ENDPATH**/ ?>